@import "tailwindcss";
@custom-variant dark (&:where(.dark, .dark *));

*{
  box-sizing: border-box;
  transition: background-color 0.2s ease, color 0.2s ease;
}
@font-face {
  font-family: 'Tajawal';
  src: url('/fonts/Tajawal-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Tajawal';
  src: url('/fonts/Tajawal-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'Tajawal';
  src: url('/fonts/Tajawal-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

body, html {
  margin: 0;
  padding: 0;
  font-family: 'Tajawal', sans-serif;
  background-color: var(--bg-primary, #f8fafc);
  color: var(--text-primary, #1e293b);
}


/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(148, 163, 184, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.4);
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.6);
}

/* سكرول للعمود */
.column-scroll::-webkit-scrollbar {
  width: 4px;
}

.column-scroll::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.3);
  border-radius: 2px;
}

.column-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}
